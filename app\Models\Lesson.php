<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Lesson extends Model
{
    protected $fillable = [
        'level',
        'title',
        'description',
        'order_index',
    ];
    
    protected $primaryKey = 'level';
    protected $keyType = 'string';

    //đ<PERSON>nh ngh<PERSON>a các quan hệ với các model khác
    public function courses()
    {
        return $this->hasMany(Course::class, 'level','level');
    }
    public function lessonParts()
    {
        // Since lesson_parts no longer has level column,
        // return all lesson_parts for now
        // TODO: Redesign relationship between lessons and lesson_parts
        return LessonPart::query();
    }
}
