# 🎉 ALL GET ROUTES TEST RESULTS

**Base URL**: http://192.168.1.6:8000/api
**Test Date**: 2025-06-29 19:20:04
**Total APIs Tested**: 53
**Successful APIs**: 53
**Failed APIs**: 0
**Success Rate**: 100%

## ✅ ALL SUCCESSFUL GET API ENDPOINTS (53)

- `http://192.168.1.6:8000/api/StudentDN/trang/123456`
- `http://192.168.1.6:8000/api/TeacherDN/johnsmith/123456`
- `http://192.168.1.6:8000/api/students`
- `http://192.168.1.6:8000/api/students/1`
- `http://192.168.1.6:8000/api/courses`
- `http://192.168.1.6:8000/api/courses/1`
- `http://192.168.1.6:8000/api/courses/1/students/count`
- `http://192.168.1.6:8000/api/courses/student/1`
- `http://192.168.1.6:8000/api/courses/level/A1`
- `http://192.168.1.6:8000/api/enrollments/course/2`
- `http://192.168.1.6:8000/api/enrollments/student/1`
- `http://192.168.1.6:8000/api/teachers`
- `http://192.168.1.6:8000/api/teachers/1`
- `http://192.168.1.6:8000/api/teachers/course/1`
- `http://192.168.1.6:8000/api/lessons/course/1`
- `http://192.168.1.6:8000/api/lessons/A1`
- `http://192.168.1.6:8000/api/lesson-parts/lesson/A1`
- `http://192.168.1.6:8000/api/lesson-parts/1`
- `http://192.168.1.6:8000/api/lesson-part-questions/1`
- `http://192.168.1.6:8000/api/lesson-parts/course/1`
- `http://192.168.1.6:8000/api/lesson-parts/1/details`
- `http://192.168.1.6:8000/api/lesson-parts/1/student/1/progress`
- `http://192.168.1.6:8000/api/scores/student/1`
- `http://192.168.1.6:8000/api/scores/lesson-part/1/student/1`
- `http://192.168.1.6:8000/api/progress/course/24/student/1`
- `http://192.168.1.6:8000/api/progress/student/1/overview`
- `http://192.168.1.6:8000/api/progress/lesson-part/1/student/1`
- `http://192.168.1.6:8000/api/progress/lesson-part/1/student/1/course/1`
- `http://192.168.1.6:8000/api/progress/lesson/A1/student/1`
- `http://192.168.1.6:8000/api/progress/lesson/A1/student/1/course/1`
- `http://192.168.1.6:8000/api/progress/course/24/student/1/detailed`
- `http://192.168.1.6:8000/api/progress/student/1/overview/detailed`
- `http://192.168.1.6:8000/api/teacher-assignments/course/1`
- `http://192.168.1.6:8000/api/teacher-assignments/1`
- `http://192.168.1.6:8000/api/teacher-assignments/teacher/1`
- `http://192.168.1.6:8000/api/questions/assignment/1`
- `http://192.168.1.6:8000/api/questions/1`
- `http://192.168.1.6:8000/api/questions/lesson-part/1`
- `http://192.168.1.6:8000/api/answers/question/1`
- `http://192.168.1.6:8000/api/student-answers/student/1/course/1/lesson-part/1`
- `http://192.168.1.6:8000/api/student-answers/recent-submission/student/1/course/1/lesson-part/1?submission_time=2025-06-29+19%3A19%3A59`
    /**
     * Lấy câu trả lời của học sinh theo course, lesson part và answered_at
     * GET /api/student-answers/student/{studentId}/course/{courseId}/lesson-part/{lessonPartId}/answered-at/{answeredAt}
     */
    public function getAnswersByStudentCourseAndLessonPartAndDate(Request $request, $studentId, $courseId, $lessonPartId, $answeredAt)
    {
        try {
            // Parse answered_at parameter (expect format: YYYY-MM-DD or YYYY-MM-DD_HH:MM:SS)
            $answeredAtDate = str_replace('_', ' ', $answeredAt);

            // Validate date format
            if (!strtotime($answeredAtDate)) {
                return response()->json([
                    'success' => false,
                    'error' => 'Invalid answered_at format. Use YYYY-MM-DD or YYYY-MM-DD_HH:MM:SS'
                ], 400);
            }

            $query = StudentAnswer::where('student_id', $studentId)
                                 ->where('course_id', $courseId)
                                 ->whereHas('Question', function($q) use ($lessonPartId) {
                                     $q->where('lesson_part_id', $lessonPartId);
                                 })
                                 ->with(['Question.lessonPart', 'Course', 'Student']);

            // Filter by specific date or date range
            if (strlen($answeredAtDate) == 10) {
                // Date only (YYYY-MM-DD) - get all answers for that day
                $startTime = $answeredAtDate . ' 00:00:00';
                $endTime = $answeredAtDate . ' 23:59:59';
                $query->whereBetween('answered_at', [$startTime, $endTime]);
            } else {
                // DateTime - get answers within 1 hour of specified time
                $startTime = date('Y-m-d H:i:s', strtotime($answeredAtDate . ' -30 minutes'));
                $endTime = date('Y-m-d H:i:s', strtotime($answeredAtDate . ' +30 minutes'));
                $query->whereBetween('answered_at', [$startTime, $endTime]);
            }

            // Additional query parameters
            if ($request->has('exact_time') && $request->exact_time == 'true') {
                // Exact timestamp match (within 1 minute)
                $startTime = date('Y-m-d H:i:s', strtotime($answeredAtDate . ' -30 seconds'));
                $endTime = date('Y-m-d H:i:s', strtotime($answeredAtDate . ' +30 seconds'));
                $query->whereBetween('answered_at', [$startTime, $endTime]);
            }

            // Order by answered_at
            $answers = $query->orderBy('answered_at', 'desc')->get();

            // Add additional computed fields
            $answers = $answers->map(function($answer) {
                $answer->lesson_part_id = $answer->Question?->lesson_part_id;
                $answer->lesson_part_name = $answer->Question?->lessonPart?->part_name;
                $answer->formatted_answered_at = $answer->answered_at?->format('Y-m-d H:i:s');
                return $answer;
            });

            return response()->json([
                'success' => true,
                'data' => $answers,
                'total_count' => $answers->count(),
                'student_id' => $studentId,
                'course_id' => $courseId,
                'lesson_part_id' => $lessonPartId,
                'answered_at_filter' => $answeredAtDate,
                'query_parameters' => [
                    'exact_time' => $request->exact_time ?? 'false'
                ]
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Lỗi server',
                'message' => $e->getMessage()
            ], 500);
        }
    }
- `http://192.168.1.6:8000/api/class-posts/course/1`
- `http://192.168.1.6:8000/api/class-posts/course/1/comments`
- `http://192.168.1.6:8000/api/class-posts/1`
- `http://192.168.1.6:8000/api/class-post-replies/post/2`
- `http://192.168.1.6:8000/api/notifications/student/1`
- `http://192.168.1.6:8000/api/notifications/student/1/unread-count`
- `http://192.168.1.6:8000/api/notifications/1`
- `http://192.168.1.6:8000/api/exam-results/student/1`
- `http://192.168.1.6:8000/api/exam-results/course/24/student/1`
- `http://192.168.1.6:8000/api/evaluations/student/1`
- `http://192.168.1.6:8000/api/statistics/overview`
- `http://192.168.1.6:8000/api/statistics/courses`
- `http://192.168.1.6:8000/api/statistics/students/performance`

